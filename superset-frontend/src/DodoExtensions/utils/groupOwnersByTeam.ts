import Owner from 'src/types/Owner';
import { t } from '@superset-ui/core';
import { createUserSelectLabel } from './createUserSelectLabel';

export const groupOwnersByTeam = (
  owners: Owner[],
): {
  selectedOptions: any[];
  groupedOwnerIdsMap: Record<number, string>;
} => {
  const groupedOwners: Record<string, Owner[]> = {};
  const ungroupedOwners: Owner[] = [];

  owners.forEach(owner => {
    const { team } = owner;

    if (!team) {
      ungroupedOwners.push(owner);
      return;
    }

    if (!groupedOwners[team]) {
      groupedOwners[team] = [];
    }
    groupedOwners[team].push(owner);
  });

  // check if there is only one owner in the team and move it to ungrouped
  Object.values(groupedOwners).forEach(owners => {
    if (owners.length === 1) {
      ungroupedOwners.push(owners[0]);
      delete groupedOwners[owners[0].team!];
    }
  });

  const teamOptions = Object.values(groupedOwners).map(owners => ({
    label: `${t('Team')}: ${owners[0].team!}`,
    value: owners[0].team!,
    disabled: true,
  }));

  const otherOwnerOptions = ungroupedOwners.map(owner =>
    createUserSelectLabel(owner, false, false),
  );

  return {
    selectedOptions: [...teamOptions, ...otherOwnerOptions],
    groupedOwnerIdsMap: Object.values(groupedOwners)
      .flat()
      .reduce((acc, owner) => ({ ...acc, [owner.id]: 'true' }), {}),
  };
};
